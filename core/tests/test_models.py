from django.test import TestCase
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.db import IntegrityError  # Add this import
from core.models import CustomUserManager

User = get_user_model()

class CustomUserManagerTest(TestCase):
    """Test cases for CustomUserManager"""

    def setUp(self):
        self.manager = CustomUserManager()
        self.manager.model = User

    def test_create_user_success(self):
        """Test creating a regular user successfully"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, 'testuser')
        self.assertTrue(user.check_password('testpass123'))
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)

    def test_create_user_without_email(self):
        """Test creating user without email raises ValueError"""
        with self.assertRaises(ValueError) as context:
            User.objects.create_user(
                email='',
                username='testuser',
                password='testpass123'
            )
        self.assertEqual(str(context.exception), "Email is required")

    def test_create_user_normalizes_email(self):
        """Test that email is normalized when creating user"""
        user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')

    def test_create_superuser_success(self):
        """Test creating a superuser successfully"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            username='admin',
            password='adminpass123'
        )
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.username, 'admin')
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)

    def test_create_superuser_with_extra_fields(self):
        """Test creating superuser with additional fields"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            username='admin',
            password='adminpass123',
            is_active=True  # Changed: This won't be overridden, just passed through
        )
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.is_active)  # Add this assertion

class UserModelTest(TestCase):
    """Test cases for User model"""

    def setUp(self):
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'testpass123'
        }

    def test_user_creation(self):
        """Test basic user creation"""
        user = User.objects.create_user(**self.user_data)
        self.assertIsInstance(user, User)
        self.assertEqual(user.email, self.user_data['email'])
        self.assertEqual(user.username, self.user_data['username'])

    def test_user_str_method(self):
        """Test user string representation"""
        user = User.objects.create_user(**self.user_data)
        self.assertEqual(str(user), 'testuser')

    def test_email_unique_constraint(self):
        """Test that email must be unique"""
        User.objects.create_user(**self.user_data)
        
        with self.assertRaises(IntegrityError):  # Fixed: Use specific exception
            User.objects.create_user(
                email='<EMAIL>',  # Same email
                username='different_user',
                password='password123'
            )

    def test_username_unique_constraint(self):
        """Test that username must be unique"""
        User.objects.create_user(**self.user_data)
        
        with self.assertRaises(IntegrityError):  # Fixed: Use specific exception
            User.objects.create_user(
                email='<EMAIL>',
                username='testuser',  # Same username
                password='password123'
            )

    def test_user_fields_default_values(self):
        """Test default values for user fields"""
        user = User.objects.create_user(**self.user_data)
        self.assertTrue(user.is_active)
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertIsNotNone(user.created_at)
        self.assertIsNotNone(user.updated_at)
        self.assertIsNone(user.last_login)

    def test_username_field_setting(self):
        """Test that USERNAME_FIELD is set to email"""
        self.assertEqual(User.USERNAME_FIELD, 'email')

    def test_required_fields_setting(self):
        """Test that REQUIRED_FIELDS contains username"""
        self.assertEqual(User.REQUIRED_FIELDS, ['username'])
