from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core import mail
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from core.views import generate_reset_token, verify_reset_token
from unittest.mock import patch

User = get_user_model()

@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class RegisterViewTest(APITestCase):
    """Test cases for RegisterView"""

    def setUp(self):
        self.url = reverse('register')
        self.valid_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'TestPass123!'
        }

    def test_register_success(self):
        """Test successful user registration"""
        response = self.client.post(self.url, self.valid_data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Check user was created
        user = User.objects.get(email=self.valid_data['email'])
        self.assertEqual(user.username, self.valid_data['username'])

    def test_register_duplicate_email(self):
        """Test registration with duplicate email"""
        User.objects.create_user(**self.valid_data)
        response = self.client.post(self.url, self.valid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_register_invalid_data(self):
        """Test registration with invalid data"""
        invalid_data = {'email': 'invalid-email'}
        response = self.client.post(self.url, invalid_data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class CustomTokenObtainPairViewTest(APITestCase):
    """Test cases for CustomTokenObtainPairView (Login)"""

    def setUp(self):
        self.url = reverse('token_obtain_pair')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )

    def test_login_success(self):
        """Test successful login"""
        data = {
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access', response.data)
        self.assertIn('refresh', response.data)

    def test_login_invalid_credentials(self):
        """Test login with invalid credentials"""
        data = {
            'email': '<EMAIL>',
            'password': 'wrongpassword'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_login_missing_email(self):
        """Test login with missing email"""
        data = {'password': 'testpass123'}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class LogoutViewTest(APITestCase):
    """Test cases for LogoutView"""

    def setUp(self):
        self.url = reverse('logout')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )
        self.refresh_token = RefreshToken.for_user(self.user)

    def test_logout_success(self):
        """Test successful logout"""
        self.client.force_authenticate(user=self.user)
        data = {'refresh': str(self.refresh_token)}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_205_RESET_CONTENT)

    def test_logout_unauthenticated(self):
        """Test logout without authentication"""
        data = {'refresh': str(self.refresh_token)}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_logout_invalid_token(self):
        """Test logout with invalid refresh token"""
        self.client.force_authenticate(user=self.user)
        data = {'refresh': 'invalid_token'}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class ForgotPasswordViewTest(APITestCase):
    """Test cases for ForgotPasswordView"""

    def setUp(self):
        self.url = reverse('forgot_password')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='testpass123'
        )

    @patch('accounts.views.send_mail')
    def test_forgot_password_success(self, mock_send_mail):
        """Test successful password reset request"""
        mock_send_mail.return_value = True
        data = {'email': '<EMAIL>'}
        response = self.client.post(self.url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('Password reset link sent', response.data['message'])
        mock_send_mail.assert_called_once()

    def test_forgot_password_user_not_found(self):
        """Test password reset for non-existent user"""
        data = {'email': '<EMAIL>'}
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_forgot_password_missing_email(self):
        """Test password reset without email"""
        response = self.client.post(self.url, {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
@override_settings(
    REST_FRAMEWORK={
        'DEFAULT_THROTTLE_CLASSES': [],
        'DEFAULT_THROTTLE_RATES': {}
    }
)
class ChangePasswordViewTest(APITestCase):
    """Test cases for ChangePasswordView"""

    def setUp(self):
        self.url = reverse('change_password')
        self.user = User.objects.create_user(
            email='<EMAIL>',
            username='testuser',
            password='oldpass123'
        )

    def test_change_password_success(self):
        """Test successful password change"""
        self.client.force_authenticate(user=self.user)
        data = {
            'current_password': 'oldpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        }
        response = self.client.post(self.url, data)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify password was changed
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('NewPass123!'))

    def test_change_password_unauthenticated(self):
        """Test password change without authentication"""
        data = {
            'current_password': 'oldpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_change_password_wrong_current(self):
        """Test password change with wrong current password"""
        self.client.force_authenticate(user=self.user)
        data = {
            'current_password': 'wrongpass',
            'new_password': 'NewPass123!',
            'confirm_password': 'NewPass123!'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_change_password_mismatch(self):
        """Test password change with mismatched passwords"""
        self.client.force_authenticate(user=self.user)
        data = {
            'current_password': 'oldpass123',
            'new_password': 'NewPass123!',
            'confirm_password': 'DifferentPass123!'
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_change_password_missing_fields(self):
        """Test password change with missing fields"""
        self.client.force_authenticate(user=self.user)
        data = {'current_password': 'oldpass123'}  # Missing other fields
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

class TokenFunctionsTest(TestCase):
    """Test cases for token utility functions"""

    def setUp(self):
        self.email = '<EMAIL>'

    def test_generate_reset_token(self):
        """Test generating reset token"""
        token = generate_reset_token(self.email)
        self.assertIsInstance(token, str)
        self.assertTrue(len(token) > 0)

    def test_verify_reset_token_valid(self):
        """Test verifying valid token"""
        token = generate_reset_token(self.email)
        verified_email = verify_reset_token(token, expiration=600)
        self.assertEqual(verified_email, self.email)

    def test_verify_reset_token_invalid(self):
        """Test verifying invalid token"""
        verified_email = verify_reset_token('invalid_token')
        self.assertIsNone(verified_email)

    def test_verify_reset_token_expired(self):
        """Test verifying expired token"""
        import time
        token = generate_reset_token(self.email)
        time.sleep(1)  # Wait a bit
        verified_email = verify_reset_token(token, expiration=0)  # Immediate expiry
        self.assertIsNone(verified_email)
