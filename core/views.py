from django.core.mail import send_mail
from itsdangerous import URLSafeTimedSerializer
from django.conf import settings
from rest_framework import generics, permissions
from .models import User
from .serializers import RegisterSerializer, UserSerializer
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.views import APIView
from rest_framework import status
from .validators import CustomPasswordValidator
from django.core.exceptions import ValidationError as DjangoValidationError
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.permissions import IsAuthenticated
from rest_framework.throttling import UserRateThrottle
from .throttles import LoginRateThrottle, PasswordResetThrottle, PasswordChangeThrottle, FailedLoginThrottle
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError

class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom login view with rate limiting
    """
    throttle_classes = [LoginRateThrottle]
    throttle_scope = 'login'
    
    def post(self, request, *args, **kwargs):
        try:
            response = super().post(request, *args, **kwargs)
            return response
        except (InvalidToken, TokenError) as e:
            # Apply stricter throttling for failed login attempts
            from .throttles import FailedLoginThrottle
            throttle = FailedLoginThrottle()
            if not throttle.allow_request(request, self):
                self.throttled(request, throttle.wait())
            raise e

class RegisterView(generics.CreateAPIView):
    queryset = User.objects.all()
    serializer_class = RegisterSerializer
    permission_classes = [permissions.AllowAny]

class LogoutView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            refresh_token = request.data["refresh"]
            token = RefreshToken(refresh_token)
            token.blacklist()
            return Response({"message": "Logged out successfully"}, status=status.HTTP_205_RESET_CONTENT)
        except Exception:
            return Response({"error": "Invalid token"}, status=status.HTTP_400_BAD_REQUEST)

def generate_reset_token(email):
    serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
    return serializer.dumps(email, salt="password-reset-salt")

def verify_reset_token(token, expiration=600):  # 10 minutes expiration
    serializer = URLSafeTimedSerializer(settings.SECRET_KEY)
    try:
        email = serializer.loads(token, salt="password-reset-salt", max_age=expiration)
        return email
    except Exception:
        return None

class ForgotPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [PasswordResetThrottle]
    throttle_scope = 'password_reset'
    def post(self, request):
        email = request.data.get('email')
        if not email:
            return Response({"error": "Email is required"}, status=400)
        try:
            user = User.objects.get(email=email)
            token = generate_reset_token(user.email)
            reset_link = f"http://192.168.1.122:3000/reset-password/{token}/"
            
            # Send email (you must configure email backend in settings)
            send_mail(
                "Password Reset Request",
                f"Click the link to reset your password: {reset_link}",
                "<EMAIL>",
                [user.email],
                fail_silently=False,
            )
            return Response({"message": "Password reset link sent to your email."})
        except User.DoesNotExist:
            return Response({"error": "User not found."}, status=404)

class ResetPasswordView(APIView):
    permission_classes = [permissions.AllowAny]
    throttle_classes = [PasswordResetThrottle]
    throttle_scope = 'password_reset'
    def post(self, request, token):
        new_password = request.data.get('password')
        if not new_password:
            return Response({"error": "Password is required"}, status=400)

        # Validate token (5-minute expiry)
        email = verify_reset_token(token, expiration=300)
        if email is None:
            return Response({"error": "Invalid or expired token."}, status=400)

        # Validate password using your custom validator
        validator = CustomPasswordValidator()
        try:
            validator.validate(new_password)
        except DjangoValidationError as e:
            raise DRFValidationError({"password": e.messages})

        # Reset the password
        try:
            user = User.objects.get(email=email)
            user.set_password(new_password)
            user.save()
            return Response({"message": "Password reset successful."})
        except User.DoesNotExist:
            return Response({"error": "User not found."}, status=404)
        
class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]
    throttle_classes = [PasswordChangeThrottle]
    throttle_scope = 'password_change'
    def post(self, request):
        user = request.user

        current_password = request.data.get("current_password")
        new_password = request.data.get("new_password")
        confirm_password = request.data.get("confirm_password")

        # Check all fields are provided
        if not all([current_password, new_password, confirm_password]):
            return Response({"error": "All fields are required."}, status=status.HTTP_400_BAD_REQUEST)

        # Check current password is correct
        if not user.check_password(current_password):
            return Response({"error": "Current password is incorrect."}, status=status.HTTP_400_BAD_REQUEST)

        # Check new and confirm password match
        if new_password != confirm_password:
            return Response({"error": "New password and confirm password do not match."}, status=status.HTTP_400_BAD_REQUEST)


        # Set new password
        user.set_password(new_password)
        user.save()

        return Response({"success": "Password changed successfully."}, status=status.HTTP_200_OK)

 
