"""
Custom password validators for enhanced security in the HR Management System.
"""

import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _


class CustomPasswordValidator:
    """
    Custom password validator that enforces additional security requirements:
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    - At least one special character
    - No common patterns
    """
    
    def validate(self, password, user=None):
        """
        Validate the password against custom security requirements.
        
        Args:
            password: The password to validate
            user: The user instance (optional)
            
        Raises:
            ValidationError: If password doesn't meet requirements
        """
        errors = []
        
        # Check for uppercase letter
        if not re.search(r'[A-Z]', password):
            errors.append(_('Password must contain at least one uppercase letter.'))
        
        # Check for lowercase letter
        if not re.search(r'[a-z]', password):
            errors.append(_('Password must contain at least one lowercase letter.'))
        
        # Check for digit
        if not re.search(r'\d', password):
            errors.append(_('Password must contain at least one digit.'))
        
        # Check for special character
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append(_('Password must contain at least one special character (!@#$%^&*(),.?":{}|<>).'))
        
        # Check for common patterns
        common_patterns = [
            r'123456',
            r'password',
            r'qwerty',
            r'abc123',
            r'admin',
            r'letmein',
        ]
        
        for pattern in common_patterns:
            if re.search(pattern, password.lower()):
                errors.append(_('Password contains a common pattern that is not allowed.'))
                break
        
        # Check for repeated characters (more than 3 in a row)
        if re.search(r'(.)\1{3,}', password):
            errors.append(_('Password cannot contain more than 3 repeated characters in a row.'))
        
        # Check for keyboard patterns
        keyboard_patterns = [
            'qwertyuiop',
            'asdfghjkl',
            'zxcvbnm',
            '1234567890',
        ]
        
        for pattern in keyboard_patterns:
            if pattern in password.lower() or pattern[::-1] in password.lower():
                errors.append(_('Password cannot contain keyboard patterns.'))
                break
        
        # If user is provided, check against user attributes
        if user:
            user_attrs = [
                getattr(user, 'username', ''),
                getattr(user, 'first_name', ''),
                getattr(user, 'last_name', ''),
                getattr(user, 'email', '').split('@')[0] if hasattr(user, 'email') else '',
            ]
            
            for attr in user_attrs:
                if attr and len(attr) > 2 and attr.lower() in password.lower():
                    errors.append(_('Password cannot contain your personal information.'))
                    break
        
        if errors:
            raise ValidationError(errors)
    
    def get_help_text(self):
        """
        Return help text for password requirements.
        """
        return _(
            'Your password must contain at least one uppercase letter, '
            'one lowercase letter, one digit, and one special character. '
            'It cannot contain common patterns or your personal information.'
        )


class OrganizationPasswordValidator:
    """
    Validator that prevents passwords containing organization-specific information.
    """
    
    def validate(self, password, user=None):
        """
        Validate that password doesn't contain organization information.
        """
        if user and hasattr(user, 'organization') and user.organization:
            org_name = user.organization.company_name.lower()
            
            # Check if organization name (or parts of it) are in password
            if len(org_name) > 3 and org_name in password.lower():
                raise ValidationError(
                    _('Password cannot contain your organization name.'),
                    code='password_contains_org_name',
                )
            
            # Check for organization name words
            org_words = org_name.split()
            for word in org_words:
                if len(word) > 3 and word in password.lower():
                    raise ValidationError(
                        _('Password cannot contain words from your organization name.'),
                        code='password_contains_org_word',
                    )
    
    def get_help_text(self):
        """
        Return help text for organization password requirements.
        """
        return _('Your password cannot contain your organization name or related words.')


class PasswordHistoryValidator:
    """
    Validator that prevents reusing recent passwords.
    Note: This would require a PasswordHistory model to track previous passwords.
    """
    
    def __init__(self, password_history_count=5):
        """
        Initialize with number of previous passwords to check.
        
        Args:
            password_history_count: Number of previous passwords to prevent reuse
        """
        self.password_history_count = password_history_count
    
    def validate(self, password, user=None):
        """
        Validate that password hasn't been used recently.
        
        Note: This is a placeholder implementation.
        In a real system, you would check against a PasswordHistory model.
        """
        # This would require implementing a PasswordHistory model
        # and checking against stored password hashes
        pass
    
    def get_help_text(self):
        """
        Return help text for password history requirements.
        """
        return _(
            f'Your password cannot be the same as your last '
            f'{self.password_history_count} passwords.'
        )
