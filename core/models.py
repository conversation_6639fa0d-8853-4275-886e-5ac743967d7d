from django.db import models
from django.contrib.auth.models import Abstract<PERSON><PERSON><PERSON><PERSON>, BaseUserManager
from django.conf import settings
from django.utils import timezone
import base64
import re


# Temporarily comment out cryptography import for testing
try:
    from cryptography.fernet import Fernet
    ENCRYPTION_AVAILABLE = True
except ImportError:
    ENCRYPTION_AVAILABLE = False
    print("Warning: cryptography not available, using TextField instead of EncryptedField")


# -----------------------------
# Encryption Utilities
# -----------------------------
class EncryptedField(models.TextField):
    """Custom field that encrypts data before storing in database."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if ENCRYPTION_AVAILABLE:
            # Generate encryption key from Django secret key
            key = base64.urlsafe_b64encode(settings.SECRET_KEY[:32].ljust(32, '0').encode())
            self.cipher = Fernet(key)
        else:
            self.cipher = None

    def from_db_value(self, value, expression, connection):
        """Decrypt value when reading from database."""
        if value is None:
            return value
        if not ENCRYPTION_AVAILABLE or not self.cipher:
            return value
        try:
            return self.cipher.decrypt(value.encode()).decode()
        except:
            # Return original value if decryption fails (for migration compatibility)
            return value

    def to_python(self, value):
        """Convert value to Python type."""
        if isinstance(value, str) or value is None:
            return value
        return str(value)

    def get_prep_value(self, value):
        """Encrypt value before storing in database."""
        if value is None:
            return value
        if not ENCRYPTION_AVAILABLE or not self.cipher:
            return value
        try:
            return self.cipher.encrypt(str(value).encode()).decode()
        except:
            return value


# -----------------------------
# Custom User Manager
# -----------------------------
class CustomUserManager(BaseUserManager):
    def create_user(self, email, username, password=None, **extra_fields):
        if not email:
            raise ValueError("Email is required")
        user = self.model(email=self.normalize_email(email), username=username, **extra_fields)
        user.set_password(password)
        user.save()
        return user

    def create_superuser(self, email, username, password=None, **extra_fields):
        extra_fields.setdefault("is_superuser", True)
        extra_fields.setdefault("is_staff", True)

        # Create a default organization for superuser if none exists
        if 'organization' not in extra_fields:
            organization, created = Organization.objects.get_or_create(
                company_name="Default Organization",
                defaults={'industry_type': 'Technology'}
            )
            extra_fields['organization'] = organization

            # Create an Admin role if it doesn't exist
            admin_role, created = Role.objects.get_or_create(
                organization=organization,
                role_name="Admin",
                defaults={'description': 'System Administrator'}
            )
            extra_fields['role'] = admin_role

        return self.create_user(email, username, password, **extra_fields)


# -----------------------------
# Organization
# -----------------------------
class Organization(models.Model):
    company_name = models.CharField(max_length=150, unique=True)
    industry_type = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.company_name

# -----------------------------
# Role
# -----------------------------
class Role(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    role_name = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.role_name

# -----------------------------
# Permission
# -----------------------------
class Permission(models.Model):
    permission_name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

# -----------------------------
# RolePermission Mapping
# -----------------------------
class RolePermission(models.Model):
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('role', 'permission')

    def __str__(self):
        return f"{self.role.role_name} - {self.permission.permission_name}"

# -----------------------------
# Department
# -----------------------------
class Department(models.Model):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    department_name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

# -----------------------------
# Custom User Model
# -----------------------------
class User(AbstractBaseUser):
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    role = models.ForeignKey(Role, on_delete=models.SET_NULL, null=True)
    profile = models.OneToOneField('profiles.Profile', on_delete=models.SET_NULL, null=True, blank=True, db_column='emp_id')
    email = models.EmailField(unique=True)
    username = models.CharField(max_length=50, unique=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)  # for admin panel
    is_superuser = models.BooleanField(default=False)  # for admin panel
    last_login = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    objects = CustomUserManager()

    def __str__(self):
        return self.username

    def has_permission(self, perm_name):
        if self.role:
            return self.role.rolepermission_set.filter(permission__permission_name=perm_name).exists()
        return False

    def has_perm(self, perm, obj=None):
        """
        Return True if the user has the specified permission. Query all
        available auth backends, but return immediately if any backend
        returns True. Thus, a user who has permission from a single auth
        backend is assumed to have permission in general.
        """
        # Superusers have all permissions
        if self.is_superuser:
            return True

        # Check custom permissions through role
        return self.has_permission(perm)

    def has_module_perms(self, app_label):
        """
        Return True if the user has any permissions in the given app label.
        Use similar logic as has_perm(), above.
        """
        # Superusers have all permissions
        if self.is_superuser:
            return True

        # For now, return True if user is staff (can be customized)
        return self.is_staff

    def get_full_name(self):
        """Return the full name for the user."""
        if self.profile:
            return f"{self.profile.first_name} {self.profile.last_name}".strip()
        return self.username

    def get_short_name(self):
        """Return the short name for the user."""
        if self.profile and self.profile.first_name:
            return self.profile.first_name
        return self.username



