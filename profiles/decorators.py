# decorators.py
from django.http import JsonResponse
from functools import wraps

def role_required(*roles):
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(self, request, *args, **kwargs):
            if request.user.is_superuser or (request.user.role and request.user.role.role_name in roles):
                return view_func(self, request, *args, **kwargs)
            return JsonResponse({"error": "Access denied. Insufficient role."}, status=403)
        return wrapper
    return decorator

