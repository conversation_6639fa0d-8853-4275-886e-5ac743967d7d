from django.db import models
from django.utils import timezone
from core.models import Organization, Department
from core.models import Encrypted<PERSON>ield

# -----------------------------
# Profile
# -----------------------------
class Profile(models.Model):
    emp_id = models.CharField(primary_key=True, max_length=20, editable=False)
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    first_name = models.Char<PERSON>ield(max_length=100)
    last_name = models.Char<PERSON>ield(max_length=100)
    gender = models.Char<PERSON><PERSON>(max_length=20)
    date_of_birth = models.DateField()
    email_id = models.Email<PERSON>ield(unique=True)
    phone_number = EncryptedField(max_length=255)  # Encrypted sensitive data
    emergency_contact_number = EncryptedField(max_length=255)  # Encrypted sensitive data
    address = EncryptedField()  # Encrypted sensitive data
    city = models.Char<PERSON>ield(max_length=100)
    state = models.Char<PERSON>ield(max_length=100)
    pincode = models.Char<PERSON><PERSON>(max_length=10)
    aadhar_number = EncryptedField(max_length=255)  # Encrypted sensitive government ID
    pan_number = EncryptedField(max_length=255)     # Encrypted sensitive government ID
    marital_status = models.CharField(max_length=20)
    designation = models.CharField(max_length=100)
    date_of_joining = models.DateField()
    work_location = models.CharField(max_length=100)
    employment_type = models.CharField(max_length=50)
    previous_company_name = models.CharField(max_length=100)
    years_of_experience = models.DecimalField(max_digits=5, decimal_places=2)
    highest_qualification = models.CharField(max_length=100)
    college_university_name = models.CharField(max_length=100)
    graduation_year = models.IntegerField()
    reporting_manager = models.ForeignKey("self", null=True, blank=True, on_delete=models.SET_NULL, related_name="subordinates")
    department_ref = models.ForeignKey(Department, null=True, blank=True, on_delete=models.SET_NULL)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def save(self, *args, **kwargs):
        """Override save method to auto-generate emp_id in format EMP<yy><nnn>"""
        if not self.emp_id:
            current_year = timezone.now().year
            year_suffix = str(current_year)[-2:]  # Last 2 digits of year

            # Find the latest emp_id for the current year
            latest_profile = Profile.objects.filter(
                emp_id__startswith=f'EMP{year_suffix}'
            ).order_by('-emp_id').first()

            if latest_profile:
                # Extract the sequence number and increment
                latest_sequence = int(latest_profile.emp_id[-3:])
                new_sequence = latest_sequence + 1
            else:
                # First profile for this year
                new_sequence = 1

            # Generate new emp_id
            self.emp_id = f'EMP{year_suffix}{new_sequence:03d}'

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.emp_id} - {self.first_name} {self.last_name}"


# -----------------------------
# Bank Details (Separate)
# -----------------------------
class BankDetails(models.Model):
    profile = models.OneToOneField(Profile, on_delete=models.CASCADE, db_column='emp_id')
    organization = models.ForeignKey(Organization, on_delete=models.CASCADE)
    bank_account_number = EncryptedField(max_length=255)  # Highly sensitive encrypted data
    bank_name = models.CharField(max_length=100)
    ifsc_code = models.CharField(max_length=20)
    uan_number = EncryptedField(max_length=255)  # Sensitive government ID encrypted
    esic_number = models.CharField(max_length=20)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Bank Details for {self.profile.emp_id}"

