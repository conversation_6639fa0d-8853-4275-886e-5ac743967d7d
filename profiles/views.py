from django.shortcuts import render
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Profile
from .serializers import ProfileSerializer
from .decorators import role_required


class ProfileViewSet(viewsets.ModelViewSet):
    queryset = Profile.objects.all()
    serializer_class = ProfileSerializer
    permission_classes = [permissions.IsAuthenticated]

    #  Access-level filter
    def get_queryset(self):
        user = self.request.user

        # HR and <PERSON><PERSON> can see all
        if user.is_superuser or user.role.role_name in ['HR', 'Admin']:
            return Profile.objects.all()

        # Access-level 1: Only own profile
        if user.access_level == 1:
            return Profile.objects.filter(emp_id=user.profile.emp_id)

        # Access-level 2: Team (e.g., same manager)
        elif user.access_level == 2:
            return Profile.objects.filter(reporting_manager=user.profile)

        # Access-level 3: Department
        elif user.access_level == 3:
            return Profile.objects.filter(department_ref=user.profile.department_ref)

        # Default fallback
        return Profile.objects.none()

    #  Only <PERSON><PERSON> can create
    @role_required('HR')
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    #  Only HR can update
    @role_required('HR')
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @role_required('HR')
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    #  Only HR can delete
    @role_required('HR')
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    #  Custom route: /profiles/me/
    @action(detail=False, methods=['get'], url_path='me')
    def me(self, request):
        profile = request.user.profile
        serializer = self.get_serializer(profile)
        return Response(serializer.data)

