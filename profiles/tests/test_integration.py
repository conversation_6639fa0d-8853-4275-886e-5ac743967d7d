# tests/test_integration.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from django.urls import reverse
from decimal import Decimal
from datetime import date

from profiles.models import Profile, BankDetails
from core.models import Organization, Department, Role

User = get_user_model()

class ProfileIntegrationTest(TestCase):
    def setUp(self):
        """Set up integration test data"""
        self.client = APIClient()
        
        # Create test organization and department
        self.organization = Organization.objects.create(
            name="Integration Test Org",
            code="INT001"
        )
        self.department = Department.objects.create(
            name="IT Department",
            organization=self.organization
        )
        
        # Create HR role and user
        self.hr_role = Role.objects.create(role_name="HR")
        self.hr_user = User.objects.create_user(
            username='hr_integration',
            email='<EMAIL>',
            password='testpass123',
            role=self.hr_role,
            access_level=3
        )

    def test_complete_profile_workflow(self):
        """Test complete profile creation and management workflow"""
        self.client.force_authenticate(user=self.hr_user)
        
        # Step 1: Create a new profile
        profile_data = {
            'organization': self.organization.id,
            'first_name': 'Integration',
            'last_name': 'Test',
            'gender': 'Male',
            'date_of_birth': '1990-01-01',
            'email_id': '<EMAIL>',
            'phone_number': '+1234567890',
            'emergency_contact_number': '+1987654321',
            'address': 'Integration Test Address',
            'city': 'Test City',
            'state': 'Test State',
            'pincode': '123456',
            'aadhar_number': '123456789012',
            'pan_number': '**********',
            'marital_status': 'Single',
            'designation': 'Test Engineer',
            'date_of_joining': '2024-01-01',
            'work_location': 'Test Office',
            'employment_type': 'Full-time',
            'previous_company_name': 'Previous Test Co.',
            'years_of_experience': '2.0',
            'highest_qualification': 'B.Tech',
            'college_university_name': 'Test University',
            'graduation_year': 2023,
            'department_ref': self.department.id
        }
        
        # Create profile
        create_url = reverse('profile-list')
        create_response = self.client.post(create_url, profile_data, format='json')
        
        self.assertEqual(create_response.status_code, status.HTTP_201_CREATED)
        created_profile_id = create_response.data['emp_id']
        
        # Step 2: Retrieve the created profile
        detail_url = reverse('profile-detail', kwargs={'pk': created_profile_id})
        detail_response = self.client.get(detail_url)
        
        self.assertEqual(detail_response.status_code, status.HTTP_200_OK)
        self.assertEqual(detail_response.data['first_name'], 'Integration')
        self.assertEqual(detail_response.data['last_name'], 'Test')
        
        # Step 3: Update the profile
        update_data = {
            'designation': 'Senior Test Engineer',
            'years_of_experience': '3.0'
        }
        
        update_response = self.client.patch(detail_url, update_data, format='json')
        
        self.assertEqual(update_response.status_code, status.HTTP_200_OK)
        self.assertEqual(update_response.data['designation'], 'Senior Test Engineer')
        
        # Step 4: Verify the profile exists in list
        list_response = self.client.get(create_url)
        
        self.assertEqual(list_response.status_code, status.HTTP_200_OK)
        profile_ids = [p['emp_id'] for p in list_response.data['results']]
        self.assertIn(created_profile_id, profile_ids)
        
        # Step 5: Delete the profile
        delete_response = self.client.delete(detail_url)
        
        self.assertEqual(delete_response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Step 6: Verify profile is deleted
        verify_delete_response = self.client.get(detail_url)
        self.assertEqual(verify_delete_response.status_code, status.HTTP_404_NOT_FOUND)

    def test_access_level_filtering(self):
        """Test access level filtering works correctly"""
        # Create profiles with different reporting structures
        manager_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Manager',
            last_name='User',
            gender='Male',
            date_of_birth=date(1980, 1, 1),
            email_id='<EMAIL>',
            phone_number='+1234567890',
            emergency_contact_number='+1987654321',
            address='Manager Address',
            city='Manager City',
            state='Manager State',
            pincode='123456',
            aadhar_number='123456789012',
            pan_number='MNGR1234M',
            marital_status='Married',
            designation='Team Lead',
            date_of_joining=date(2020, 1, 1),
            work_location='Main Office',
            employment_type='Full-time',
            previous_company_name='Previous Manager Co.',
            years_of_experience=Decimal('8.0'),
            highest_qualification='MBA',
            college_university_name='Manager University',
            graduation_year=2015,
            department_ref=self.department
        )
        
        subordinate_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Subordinate',
            last_name='User',
            gender='Female',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='+1234567891',
            emergency_contact_number='+1987654322',
            address='Subordinate Address',
            city='Subordinate City',
            state='Subordinate State',
            pincode='123457',
            aadhar_number='123456789013',
            pan_number='SUBR1234S',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Main Office',
            employment_type='Full-time',
            previous_company_name='Previous Sub Co.',
            years_of_experience=Decimal('2.0'),
            highest_qualification='B.Tech',
            college_university_name='Sub University',
            graduation_year=2022,
            department_ref=self.department,
            reporting_manager=manager_profile
        )
        
        # Create manager user with access level 2 (team access)
        manager_role = Role.objects.create(role_name="Manager")
        manager_user = User.objects.create_user(
            username='manager_user',
            email='<EMAIL>',
            password='testpass123',
            role=manager_role,
            access_level=2
        )
        manager_user.profile = manager_profile
        manager_user.save()
        
        # Test manager can see their subordinates
        self.client.force_authenticate(user=manager_user)
        list_url = reverse('profile-list')
        response = self.client.get(list_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Manager should see subordinate's profile
        profile_ids = [p['emp_id'] for p in response.data['results']]
        self.assertIn(subordinate_profile.emp_id, profile_ids)
