# tests/test_views.py
import pytest
from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import Mock

from profiles.models import Profile, BankDetails
from core.models import Organization, Department, Role
from decimal import Decimal
from datetime import date

User = get_user_model()

class ProfileViewSetTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create organization and department
        self.organization = Organization.objects.create(
            name="Test Organization",
            code="TEST001"
        )
        self.department = Department.objects.create(
            name="Engineering",
            organization=self.organization
        )
        
        # Create roles
        self.hr_role = Role.objects.create(role_name="HR")
        self.admin_role = Role.objects.create(role_name="Admin")
        self.employee_role = Role.objects.create(role_name="Employee")
        
        # Create users with different roles
        self.hr_user = User.objects.create_user(
            username='hr_user',
            email='<EMAIL>',
            password='testpass123',
            role=self.hr_role,
            access_level=3
        )
        
        self.admin_user = User.objects.create_user(
            username='admin_user',
            email='<EMAIL>',
            password='testpass123',
            role=self.admin_role,
            access_level=3
        )
        
        self.employee_user = User.objects.create_user(
            username='employee_user',
            email='<EMAIL>',
            password='testpass123',
            role=self.employee_role,
            access_level=1
        )
        
        # Create profiles
        self.hr_profile = Profile.objects.create(
            organization=self.organization,
            first_name='HR',
            last_name='User',
            gender='Male',
            date_of_birth=date(1985, 1, 1),
            email_id='<EMAIL>',
            phone_number='+1234567890',
            emergency_contact_number='+1987654321',
            address='HR Address',
            city='HR City',
            state='HR State',
            pincode='123456',
            aadhar_number='123456789012',
            pan_number='**********',
            marital_status='Single',
            designation='HR Manager',
            date_of_joining=date(2020, 1, 1),
            work_location='Main Office',
            employment_type='Full-time',
            previous_company_name='Previous HR Co.',
            years_of_experience=Decimal('5.0'),
            highest_qualification='MBA',
            college_university_name='HR University',
            graduation_year=2019,
            department_ref=self.department
        )
        
        self.employee_profile = Profile.objects.create(
            organization=self.organization,
            first_name='Employee',
            last_name='User',
            gender='Female',
            date_of_birth=date(1990, 1, 1),
            email_id='<EMAIL>',
            phone_number='+1234567891',
            emergency_contact_number='+1987654322',
            address='Employee Address',
            city='Employee City',
            state='Employee State',
            pincode='123457',
            aadhar_number='123456789013',
            pan_number='EMPPAN1234F',
            marital_status='Married',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Main Office',
            employment_type='Full-time',
            previous_company_name='Previous Emp Co.',
            years_of_experience=Decimal('2.0'),
            highest_qualification='B.Tech',
            college_university_name='Emp University',
            graduation_year=2022,
            department_ref=self.department,
            reporting_manager=self.hr_profile
        )
        
        # Link users to profiles
        self.hr_user.profile = self.hr_profile
        self.hr_user.save()
        self.employee_user.profile = self.employee_profile
        self.employee_user.save()

    def test_list_profiles_hr_access(self):
        """Test HR can view all profiles"""
        self.client.force_authenticate(user=self.hr_user)
        url = reverse('profile-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)

    def test_list_profiles_employee_access(self):
        """Test employee can only view own profile"""
        self.client.force_authenticate(user=self.employee_user)
        url = reverse('profile-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['emp_id'], self.employee_profile.emp_id)

    def test_create_profile_hr_success(self):
        """Test HR can create profiles"""
        self.client.force_authenticate(user=self.hr_user)
        url = reverse('profile-list')
        
        profile_data = {
            'organization': self.organization.id,
            'first_name': 'New',
            'last_name': 'Employee',
            'gender': 'Male',
            'date_of_birth': '1992-01-01',
            'email_id': '<EMAIL>',
            'phone_number': '+1234567892',
            'emergency_contact_number': '+1987654323',
            'address': 'New Address',
            'city': 'New City',
            'state': 'New State',
            'pincode': '123458',
            'aadhar_number': '123456789014',
            'pan_number': 'NEWPAN1234F',
            'marital_status': 'Single',
            'designation': 'Junior Developer',
            'date_of_joining': '2024-01-01',
            'work_location': 'Main Office',
            'employment_type': 'Full-time',
            'previous_company_name': 'Previous New Co.',
            'years_of_experience': '1.0',
            'highest_qualification': 'B.Tech',
            'college_university_name': 'New University',
            'graduation_year': 2023,
            'department_ref': self.department.id
        }
        
        response = self.client.post(url, profile_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Profile.objects.count(), 3)

    def test_create_profile_employee_denied(self):
        """Test employee cannot create profiles"""
        self.client.force_authenticate(user=self.employee_user)
        url = reverse('profile-list')
        
        profile_data = {
            'organization': self.organization.id,
            'first_name': 'Unauthorized',
            'last_name': 'User',
            'email_id': '<EMAIL>'
        }
        
        response = self.client.post(url, profile_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertIn('Access denied', response.json()['error'])

    def test_update_profile_hr_success(self):
        """Test HR can update profiles"""
        self.client.force_authenticate(user=self.hr_user)
        url = reverse('profile-detail', kwargs={'pk': self.employee_profile.emp_id})
        
        update_data = {
            'designation': 'Senior Software Engineer'
        }
        
        response = self.client.patch(url, update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.employee_profile.refresh_from_db()
        self.assertEqual(self.employee_profile.designation, 'Senior Software Engineer')

    def test_update_profile_employee_denied(self):
        """Test employee cannot update profiles"""
        self.client.force_authenticate(user=self.employee_user)
        url = reverse('profile-detail', kwargs={'pk': self.employee_profile.emp_id})
        
        update_data = {
            'designation': 'Senior Software Engineer'
        }
        
        response = self.client.patch(url, update_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_delete_profile_hr_success(self):
        """Test HR can delete profiles"""
        self.client.force_authenticate(user=self.hr_user)
        url = reverse('profile-detail', kwargs={'pk': self.employee_profile.emp_id})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertFalse(Profile.objects.filter(emp_id=self.employee_profile.emp_id).exists())

    def test_delete_profile_employee_denied(self):
        """Test employee cannot delete profiles"""
        self.client.force_authenticate(user=self.employee_user)
        url = reverse('profile-detail', kwargs={'pk': self.employee_profile.emp_id})
        
        response = self.client.delete(url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_me_endpoint_success(self):
        """Test /profiles/me/ endpoint returns user's own profile"""
        self.client.force_authenticate(user=self.employee_user)
        url = reverse('profile-me')
        
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['emp_id'], self.employee_profile.emp_id)

    def test_unauthenticated_access_denied(self):
        """Test unauthenticated access is denied"""
        url = reverse('profile-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_superuser_access(self):
        """Test superuser has full access"""
        superuser = User.objects.create_superuser(
            username='superuser',
            email='<EMAIL>',
            password='superpass123'
        )
        
        self.client.force_authenticate(user=superuser)
        url = reverse('profile-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
