# tests/test_serializers.py
from django.test import TestCase
from decimal import Decimal
from datetime import date

from profiles.serializers import ProfileSerializer
from profiles.models import Profile
from core.models import Organization, Department

class ProfileSerializerTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name="Test Organization",
            code="TEST001"
        )
        self.department = Department.objects.create(
            name="Engineering",
            organization=self.organization
        )
        
        self.valid_profile_data = {
            'organization': self.organization.id,
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'gender': 'Male',
            'date_of_birth': '1990-01-15',
            'email_id': '<EMAIL>',
            'phone_number': '+1234567890',
            'emergency_contact_number': '+1987654321',
            'address': '123 Test Street',
            'city': 'Test City',
            'state': 'Test State',
            'pincode': '123456',
            'aadhar_number': '123456789012',
            'pan_number': '**********',
            'marital_status': 'Single',
            'designation': 'Software Engineer',
            'date_of_joining': '2023-01-01',
            'work_location': 'Test Office',
            'employment_type': 'Full-time',
            'previous_company_name': 'Previous Co.',
            'years_of_experience': '2.5',
            'highest_qualification': 'Bachelor of Engineering',
            'college_university_name': 'Test University',
            'graduation_year': 2022,
            'department_ref': self.department.id
        }

    def test_valid_serializer(self):
        """Test serializer with valid data"""
        serializer = ProfileSerializer(data=self.valid_profile_data)
        
        self.assertTrue(serializer.is_valid())
        profile = serializer.save()
        
        self.assertEqual(profile.first_name, 'John')
        self.assertEqual(profile.last_name, 'Doe')
        self.assertEqual(profile.email_id, '<EMAIL>')
        self.assertIsNotNone(profile.emp_id)

    def test_invalid_email_format(self):
        """Test serializer with invalid email format"""
        invalid_data = self.valid_profile_data.copy()
        invalid_data['email_id'] = 'invalid-email'
        
        serializer = ProfileSerializer(data=invalid_data)
        
        self.assertFalse(serializer.is_valid())
        self.assertIn('email_id', serializer.errors)

    def test_read_only_fields(self):
        """Test that read-only fields cannot be set"""
        data_with_readonly = self.valid_profile_data.copy()
        data_with_readonly['emp_id'] = 'CUSTOM001'
        data_with_readonly['created_at'] = '2023-01-01T00:00:00Z'
        
        serializer = ProfileSerializer(data=data_with_readonly)
        
        self.assertTrue(serializer.is_valid())
        profile = serializer.save()
        
        # emp_id should be auto-generated, not the provided value
        self.assertNotEqual(profile.emp_id, 'CUSTOM001')
        self.assertTrue(profile.emp_id.startswith('EMP'))

    def test_serializer_representation(self):
        """Test serializer output representation"""
        profile = Profile.objects.create(
            organization=self.organization,
            first_name='Jane',
            last_name='Smith',
            gender='Female',
            date_of_birth=date(1992, 5, 20),
            email_id='<EMAIL>',
            phone_number='+1234567891',
            emergency_contact_number='+1987654322',
            address='456 Another Street',
            city='Another City',
            state='Another State',
            pincode='654321',
            aadhar_number='************',
            pan_number='**********',
            marital_status='Married',
            designation='Data Scientist',
            date_of_joining=date(2023, 6, 1),
            work_location='Remote',
            employment_type='Full-time',
            previous_company_name='Data Corp',
            years_of_experience=Decimal('3.0'),
            highest_qualification='Master of Science',
            college_university_name='Data University',
            graduation_year=2021,
            department_ref=self.department
        )
        
        serializer = ProfileSerializer(profile)
        data = serializer.data
        
        self.assertEqual(data['first_name'], 'Jane')
        self.assertEqual(data['last_name'], 'Smith')
        self.assertEqual(data['email_id'], '<EMAIL>')
        self.assertIn('emp_id', data)
        self.assertIn('created_at', data)
        self.assertIn('updated_at', data)
