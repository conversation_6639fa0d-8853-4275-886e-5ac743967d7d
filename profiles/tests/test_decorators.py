# tests/test_decorators.py
from django.test import TestCase, RequestFactory
from django.contrib.auth import get_user_model
from django.http import JsonResponse
from unittest.mock import Mock

from profiles.decorators import role_required
from core.models import Role

User = get_user_model()

class RoleRequiredDecoratorTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.factory = RequestFactory()
        
        # Create roles
        self.hr_role = Role.objects.create(role_name="HR")
        self.admin_role = Role.objects.create(role_name="Admin")
        self.employee_role = Role.objects.create(role_name="Employee")
        
        # Create users
        self.hr_user = User.objects.create_user(
            username='hr_user',
            email='<EMAIL>',
            password='testpass123',
            role=self.hr_role
        )
        
        self.admin_user = User.objects.create_user(
            username='admin_user',
            email='<EMAIL>',
            password='testpass123',
            role=self.admin_role
        )
        
        self.employee_user = User.objects.create_user(
            username='employee_user',
            email='<EMAIL>',
            password='testpass123',
            role=self.employee_role
        )
        
        self.superuser = User.objects.create_superuser(
            username='superuser',
            email='<EMAIL>',
            password='superpass123'
        )

    def test_role_required_single_role_success(self):
        """Test decorator allows access for correct single role"""
        @role_required('HR')
        def test_view(self, request):
            return JsonResponse({"status": "success"})
        
        # Create mock view instance
        view_instance = Mock()
        request = self.factory.get('/')
        request.user = self.hr_user
        
        response = test_view(view_instance, request)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

    def test_role_required_multiple_roles_success(self):
        """Test decorator allows access for any of multiple required roles"""
        @role_required('HR', 'Admin')
        def test_view(self, request):
            return JsonResponse({"status": "success"})
        
        view_instance = Mock()
        
        # Test with HR user
        request = self.factory.get('/')
        request.user = self.hr_user
        response = test_view(view_instance, request)
        self.assertEqual(response.status_code, 200)
        
        # Test with Admin user
        request.user = self.admin_user
        response = test_view(view_instance, request)
        self.assertEqual(response.status_code, 200)

    def test_role_required_access_denied(self):
        """Test decorator denies access for incorrect role"""
        @role_required('HR')
        def test_view(self, request):
            return JsonResponse({"status": "success"})
        
        view_instance = Mock()
        request = self.factory.get('/')
        request.user = self.employee_user
        
        response = test_view(view_instance, request)
        
        self.assertEqual(response.status_code, 403)
        self.assertIn('Access denied', response.json()['error'])

    def test_superuser_access_allowed(self):
        """Test superuser bypasses role restrictions"""
        @role_required('HR')
        def test_view(self, request):
            return JsonResponse({"status": "success"})
        
        view_instance = Mock()
        request = self.factory.get('/')
        request.user = self.superuser
        
        response = test_view(view_instance, request)
        
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json()['status'], 'success')

    def test_user_without_role_denied(self):
        """Test user without role is denied access"""
        user_no_role = User.objects.create_user(
            username='norole_user',
            email='<EMAIL>',
            password='testpass123'
        )
        
        @role_required('HR')
        def test_view(self, request):
            return JsonResponse({"status": "success"})
        
        view_instance = Mock()
        request = self.factory.get('/')
        request.user = user_no_role
        
        response = test_view(view_instance, request)
        
        self.assertEqual(response.status_code, 403)
        self.assertIn('Access denied', response.json()['error'])
