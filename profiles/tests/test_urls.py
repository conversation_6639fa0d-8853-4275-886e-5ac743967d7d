# tests/test_urls.py
from django.test import TestCase
from django.urls import reverse, resolve
from profiles.views import ProfileViewSet

class ProfileURLTest(TestCase):
    def test_profile_list_url(self):
        """Test profile list URL resolution"""
        url = reverse('profile-list')
        resolver = resolve(url)
        
        self.assertEqual(resolver.func.cls, ProfileViewSet)

    def test_profile_detail_url(self):
        """Test profile detail URL resolution"""
        url = reverse('profile-detail', kwargs={'pk': 'EMP24001'})
        resolver = resolve(url)
        
        self.assertEqual(resolver.func.cls, ProfileViewSet)

    def test_profile_me_url(self):
        """Test profile me URL resolution"""
        url = reverse('profile-me')
        resolver = resolve(url)
        
        self.assertEqual(resolver.func.cls, ProfileViewSet)
        self.assertEqual(resolver.url_name, 'profile-me')