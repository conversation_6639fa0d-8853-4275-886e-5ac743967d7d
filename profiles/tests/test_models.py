# tests/test_models.py
import pytest
from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.utils import timezone
from datetime import date, datetime
from decimal import Decimal

from profiles.models import Profile, BankDetails
from core.models import Organization, Department
from django.contrib.auth import get_user_model

User = get_user_model()

class ProfileModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name="Test Organization",
            code="TEST001"
        )
        self.department = Department.objects.create(
            name="Engineering",
            organization=self.organization
        )
        
        self.profile_data = {
            'organization': self.organization,
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'gender': 'Male',
            'date_of_birth': date(1990, 1, 15),
            'email_id': '<EMAIL>',
            'phone_number': '+**********',
            'emergency_contact_number': '+**********',
            'address': '123 Test Street, Test City',
            'city': 'Test City',
            'state': 'Test State',
            'pincode': '123456',
            'aadhar_number': '**********12',
            'pan_number': '**********',
            'marital_status': 'Single',
            'designation': 'Software Engineer',
            'date_of_joining': date(2023, 1, 1),
            'work_location': 'Test Office',
            'employment_type': 'Full-time',
            'previous_company_name': 'Previous Co.',
            'years_of_experience': Decimal('2.5'),
            'highest_qualification': 'Bachelor of Engineering',
            'college_university_name': 'Test University',
            'graduation_year': 2022,
            'department_ref': self.department
        }

    def test_profile_creation_success(self):
        """Test successful profile creation"""
        profile = Profile.objects.create(**self.profile_data)
        
        self.assertIsNotNone(profile.emp_id)
        self.assertTrue(profile.emp_id.startswith('EMP'))
        self.assertEqual(profile.first_name, 'John')
        self.assertEqual(profile.last_name, 'Doe')
        self.assertEqual(profile.email_id, '<EMAIL>')

    def test_emp_id_auto_generation(self):
        """Test automatic employee ID generation"""
        current_year = timezone.now().year
        year_suffix = str(current_year)[-2:]
        
        profile = Profile.objects.create(**self.profile_data)
        expected_emp_id = f'EMP{year_suffix}001'
        
        self.assertEqual(profile.emp_id, expected_emp_id)

    def test_emp_id_sequential_generation(self):
        """Test sequential employee ID generation"""
        current_year = timezone.now().year
        year_suffix = str(current_year)[-2:]
        
        # Create first profile
        profile1 = Profile.objects.create(**self.profile_data)
        
        # Create second profile with different email
        profile_data_2 = self.profile_data.copy()
        profile_data_2['email_id'] = '<EMAIL>'
        profile2 = Profile.objects.create(**profile_data_2)
        
        self.assertEqual(profile1.emp_id, f'EMP{year_suffix}001')
        self.assertEqual(profile2.emp_id, f'EMP{year_suffix}002')

    def test_unique_email_constraint(self):
        """Test email uniqueness constraint"""
        Profile.objects.create(**self.profile_data)
        
        # Try to create another profile with same email
        profile_data_2 = self.profile_data.copy()
        profile_data_2['first_name'] = 'Jane'
        
        with self.assertRaises(IntegrityError):
            Profile.objects.create(**profile_data_2)

    def test_self_referencing_manager(self):
        """Test manager-subordinate relationship"""
        manager = Profile.objects.create(**self.profile_data)
        
        # Create subordinate
        subordinate_data = self.profile_data.copy()
        subordinate_data['email_id'] = '<EMAIL>'
        subordinate_data['reporting_manager'] = manager
        subordinate = Profile.objects.create(**subordinate_data)
        
        self.assertEqual(subordinate.reporting_manager, manager)
        self.assertIn(subordinate, manager.subordinates.all())

    def test_profile_str_representation(self):
        """Test string representation of profile"""
        profile = Profile.objects.create(**self.profile_data)
        expected_str = f"{profile.emp_id} - John Doe"
        self.assertEqual(str(profile), expected_str)

    def test_created_updated_timestamps(self):
        """Test automatic timestamp fields"""
        before_creation = timezone.now()
        profile = Profile.objects.create(**self.profile_data)
        after_creation = timezone.now()
        
        self.assertGreaterEqual(profile.created_at, before_creation)
        self.assertLessEqual(profile.created_at, after_creation)
        self.assertEqual(profile.created_at, profile.updated_at)

class BankDetailsModelTest(TestCase):
    def setUp(self):
        """Set up test data"""
        self.organization = Organization.objects.create(
            name="Test Organization",
            code="TEST001"
        )
        
        self.profile = Profile.objects.create(
            organization=self.organization,
            first_name='John',
            last_name='Doe',
            gender='Male',
            date_of_birth=date(1990, 1, 15),
            email_id='<EMAIL>',
            phone_number='+**********',
            emergency_contact_number='+**********',
            address='123 Test Street',
            city='Test City',
            state='Test State',
            pincode='123456',
            aadhar_number='**********12',
            pan_number='**********',
            marital_status='Single',
            designation='Software Engineer',
            date_of_joining=date(2023, 1, 1),
            work_location='Test Office',
            employment_type='Full-time',
            previous_company_name='Previous Co.',
            years_of_experience=Decimal('2.5'),
            highest_qualification='Bachelor',
            college_university_name='Test University',
            graduation_year=2022
        )
        
        self.bank_data = {
            'profile': self.profile,
            'organization': self.organization,
            'bank_account_number': '**********123456',
            'bank_name': 'Test Bank',
            'ifsc_code': 'TEST0001234',
            'uan_number': '**********12',
            'esic_number': '**********1234567'
        }

    def test_bank_details_creation(self):
        """Test bank details creation"""
        bank_details = BankDetails.objects.create(**self.bank_data)
        
        self.assertEqual(bank_details.profile, self.profile)
        self.assertEqual(bank_details.bank_name, 'Test Bank')
        self.assertEqual(bank_details.ifsc_code, 'TEST0001234')

    def test_one_to_one_relationship(self):
        """Test one-to-one relationship with profile"""
        bank_details = BankDetails.objects.create(**self.bank_data)
        
        # Try to create another bank details for same profile
        with self.assertRaises(IntegrityError):
            BankDetails.objects.create(**self.bank_data)

    def test_bank_details_str_representation(self):
        """Test string representation"""
        bank_details = BankDetails.objects.create(**self.bank_data)
        expected_str = f"Bank Details for {self.profile.emp_id}"
        self.assertEqual(str(bank_details), expected_str)
